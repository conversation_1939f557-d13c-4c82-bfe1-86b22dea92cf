import json
from typing import AsyncGenerator
from pathlib import Path

from google.adk.agents import Agent
from google.adk.tools import FunctionTool, agent_tool, google_search
from google.adk.agents.invocation_context import InvocationContext
from google.adk.events import Event, EventActions
from google.genai.types import Content, Part, FunctionCall
import os
# from backend.app.jmsc_concierge.sub_agents.product_expert_agent.db_agent.agent import execute_sql_query
from .sub_agents.product_expert_agent.agent import execute_sql_query
from .sub_agents.product_expert_agent.recommendation_agent import recommendation_agent
from .sub_agents.recipe_agent.agent import recipe_agent

# Create a FunctionTool from our database query function
database_query_tool = FunctionTool(func=execute_sql_query)
recipe_agent_tool = agent_tool.AgentTool(agent=recipe_agent)

# Configuration file path (must match main.py)
CONFIG_FILE_PATH = Path("config.json")


# Function to read agent configuration
def read_agent_config():
    if CONFIG_FILE_PATH.exists():
        with open(CONFIG_FILE_PATH, "r") as f:
            return json.load(f)
    return {"product_category": "All", "recommendation_preference": "all_products"}  # Default config


class RootCoordinatorAgent(Agent):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    async def _run_async_impl(self, ctx: InvocationContext) -> AsyncGenerator[Event, None]:
        # check user intent for recipe first
        user_query = ""
        if ctx.user_content and ctx.user_content.parts:
            user_query = ctx.user_content.parts[0].text.lower()

        recipe_keywords = ["recipe", "make", "cocktail", "mix", "ingredients for"]
        is_recipe_query = any(keyword in user_query for keyword in recipe_keywords)

        # Handle recipe queries by directly calling the google_search tool
        if is_recipe_query:
            search_query = f"recipe for {user_query}"
            print(f"[RootCoordinatorAgent]: Detected recipe query. Calling google_search with '{search_query}'")
            yield Event(
                author=self.name,
                content=Content(
                    parts=[
                        Part(function_call=FunctionCall(
                            name=google_search.name,
                            args={"query": search_query}
                        ))
                    ]
                )
            )
            return

        # Let the base Agent (LLM) process the input and generate events (including tool calls)
        async for event in super()._run_async_impl(ctx):
            # NEW: Check if this event is calling the database tool
            if (event.content and event.content.parts
                and hasattr(event.content.parts[0], 'function_call')
                and event.content.parts[0].function_call
                    and event.content.parts[0].function_call.name == database_query_tool.name):

                # Trigger loading state BEFORE calling database tool
                yield Event(
                    author=self.name,
                    content=Content(parts=[Part.from_text(text="")]),
                    actions=EventActions(
                        state_delta={"ui_state": {"loading_products": True, "loading_type": "database_query"}}
                    )
                )

            # check if the event is a tool output from google_search
            if event.tool_outputs and event.tool_outputs.get("tool_name") == recipe_agent_tool.name:
                tool_output = event.tool_outputs.get("result")
                # check if the original query was for a recipe
                if any(keyword in user_query for keyword in recipe_keywords):
                    # format the search result into the recipe JSON
                    # Simplified formatting
                    recipe_html = f"<div><h1>Recipe from the web</h1><p>{tool_output}</p></div>"
                    recipe_json = {
                        "status": "success",
                        "data": {
                            "recipe": recipe_html
                        }
                    }
                    yield Event(
                        author=self.name,
                        content=Content(
                            parts=[Part.from_data(data=json.dumps(recipe_json).encode(
                                'utf-8'), mime_type="application/json")]
                        ),
                        actions=EventActions(turn_complete=True)
                    )
                    ctx.end_invocation = True
                    return
                else:
                    # Handle non-recipe google search output
                    yield event
                    continue

            # Check if the event is a tool output from our database_query_tool
            if event.tool_outputs and event.tool_outputs.get("tool_name") == database_query_tool.name:
                tool_output = event.tool_outputs.get("result")
                print(f"[RootCoordinatorAgent]: Intercepted database_query_tool output: {tool_output}")
                ctx.session.state["database_output"] = tool_output

                # The database_tool now handles fetching product info internally,
                # so no explicit call to product_info_tool is needed here.
                # The 'database_output' will contain the merged data if products were found.

                # After database query (which now includes product info), call the recommendation agent
                recommendation_tool = agent_tool.AgentTool(agent=recommendation_agent)

                async for recommendation_event in recommendation_tool.run_async(ctx):
                    # Check if the recommendation agent returned a text response indicating no products
                    if recommendation_event.content and recommendation_event.content.parts and \
                       recommendation_event.content.parts[0].text == "I'm sorry, it looks like we're currently out of that.":
                        # If no products, directly query categories without triggering the interceptor again
                        print("[RootCoordinatorAgent]: Recommendation agent returned no products. Querying categories directly.")

                        # Clear loading state when no products found
                        yield Event(
                            author=self.name,
                            content=Content(parts=[Part.from_text(text="")]),
                            actions=EventActions(
                                state_delta={"ui_state": {"loading_products": False, "error": "No products found"}}
                            )
                        )

                        # Directly execute category query to avoid infinite loop
                        try:
                            category_result = execute_sql_query(
                                "SELECT DISTINCT main_category FROM recommendation_view WHERE stock_qty > 0 LIMIT 10", ctx)
                            if category_result.get("status") == "success" and category_result.get("rows"):
                                categories = [row[0] for row in category_result["rows"][:3]]
                                category_text = f"We might not have that specific item, but we have a great selection in categories like {', '.join(categories)}. Would you like to know more about any of these?"
                            else:
                                category_text = "I couldn't find any categories at the moment."

                            yield Event(
                                author=self.name,
                                content=Content(parts=[Part.from_text(text=category_text)]),
                                actions=EventActions(turn_complete=True)
                            )
                        except Exception as e:
                            print(f"[RootCoordinatorAgent]: Error querying categories: {e}")
                            yield Event(
                                author=self.name,
                                content=Content(parts=[Part.from_text(
                                    text="I'm sorry, it looks like we're currently out of that.")]),
                                actions=EventActions(turn_complete=True)
                            )
                        return  # Exit to prevent further processing
                    elif recommendation_event.content and recommendation_event.content.parts and \
                            recommendation_event.content.parts[0].mime_type == "application/json":
                        # If recommendation agent successfully generated JSON, end the invocation
                        print(
                            "[RootCoordinatorAgent]: Recommendation agent generated JSON. Ending invocation to prevent further LLM text.")

                        # Clear loading state when products are successfully found
                        yield Event(
                            author=self.name,
                            content=Content(parts=[Part.from_text(text="")]),
                            actions=EventActions(
                                state_delta={"ui_state": {"loading_products": False}}
                            )
                        )

                        yield recommendation_event  # Yield the JSON event
                        ctx.end_invocation = True  # Signal framework to stop processing for this turn
                        return  # Stop this agent's execution for this turn
                    else:
                        yield recommendation_event
                continue  # Skip further processing of this event

            # Yield all other events as they are (conversational, other tool calls, etc.)
            yield event


# Read initial configuration
current_config = read_agent_config()
product_category = current_config.get("product_category")
recommendation_preference = current_config.get("recommendation_preference")

ROOT_AGENT_PROMPT = """You are a friendly and knowledgeable AI assistant embodying the owner of a liquor store. Your primary goal is to provide excellent customer service by helping users find the perfect drink through natural, human-like conversation and intelligent product recommendations.

**Your Operational Parameters (Internal Use Only - Do Not Disclose to User):**
These parameters define your current recommendation strategy and are pre-configured. Do not attempt to dynamically access or modify them from the conversation context.
- Preferred Product Category for Recommendations: {product_category}
- Primary Recommendation Focus: {recommendation_preference}

**Core Directives:**

   1.  **Persona:** Act as a friendly, human store owner. Greet customers warmly (e.g., "Welcome in! What can I help you find today?") and guide them with gentle questions.

   2.  **Language Adaptation:** If the user initiates conversation in a language other than English , you MUST detect their language and respond in that same language. Maintain your friendly store owner persona regardless of the language.

   3.  **Tool Use Strategy:**
       *   For recipe search and genral liquer question you can use `recipe_agent_tool`.
       *   For product-related questions (e.g., "Do you have gin?", "Show me vodkas", "What whiskeys are in stock?", "What's the price of X?"), you MUST use the `execute_sql_query` tool to find relevant products.
       *   After successfully querying for products using `execute_sql_query`, the tool will internally retrieve detailed product information like descriptions and images. You do NOT need to call a separate tool for this. DO NOT ask the user for product codes.
       *   If the `execute_sql_query` tool (which now includes product info retrieval) does not return details for a specific product, or if it encounters an error during its internal product info fetch, you should still proceed to provide information for that product using the data available from the initial database query.
       
   3.  **Database Schema and Query Rules (for `execute_sql_query` tool):**
       *   The `{view_name}` table has columns: `item_code`, `item_name`, `pur_price`, `sale_price`, `stock_qty`, `size`, `vendor`, `main_category`, `location`.
       *   When querying for products, you MUST always include `stock_qty > 0` in your WHERE clause to ensure only available products are shown.
       *   When querying for products or categories, you MUST use the `LIKE` operator with `%` wildcards for `main_category`, `vendor`, or `item_name` for filtering. The LLM should infer whether the user's query refers to a product name or a category name. If the user's query could ambiguously refer to either a product name or a category (e.g., "Do you have wine?"), the query should attempt to search both `main_category` and `item_name` using `OR` (e.g., `SELECT * FROM {view_name} WHERE (main_category LIKE '%Wine%' OR item_name LIKE '%Wine%') AND stock_qty > 0 LIMIT 5`).
       *   Handle price preferences with `ORDER BY sale_price ASC` or `DESC`.
       *   Limit results to a reasonable number (e.g., `LIMIT 5`) unless the user asks for more.
       *   when you find the products envery time you need to give location, location is where this product we holds.
       *   Always give full data what we have in database like always do select * then do any condtions 
       
   4.  **Response Format Rules:**
       *   You will receive structured data (JSON) from your tools in a `tool_outputs` field. This data is for your internal use ONLY.
       *   When the system has processed a product recommendation (i.e., after `recommendation_agent` has run and found products), it will automatically format and output the data to the frontend. The `recommendation_agent` will also generate a conversational text response. Your role is to ensure the correct tools are called to gather the necessary product information.
       *   **Applying Pre-configured Parameters:** When a user's query is general or for recommendations, prioritize products from the "{product_category}" category. If the primary recommendation focus is "{recommendation_preference}", incorporate this into your SQL query (e.g., for "high_margin" consider `pur_price` and `sale_price` difference; for "over_stock" filter by `stock_qty` > a reasonable threshold).
       *   **Sorting:** Apply `ORDER BY sale_price ASC` or `DESC` if price preference is indicated.
       *   **For all other responses (e.g., greetings, errors, category suggestions, or if no product recommendation is generated):** Your final response to the user MUST be natural, conversational language. NEVER show raw `tool_outputs` or any JSON directly to the user. Do not use words like "status", "columns", "rows", "item_name", etc., in your conversational responses.
       *   Nver told stock_qty to customber because we not want to talk stock_qty to end user how many items we have, you can only use stock_qty for filter only. 
       
   5.  **How to Process and Respond to `execute_sql_query` Tool Output:**
       *   **If `execute_sql_query` returns product data (i.e., `tool_output.status` is "success" and `tool_output.products` is not empty):**
           *   **Action:** The `recommendation_agent` will handle generating both the conversational text and the JSON output for the frontend. Your role is to ensure the `execute_sql_query` tool is called with the correct product query, and then the `recommendation_agent` is invoked.
       *   **If `execute_sql_query` returns no products (i.e., `tool_output.status` is "success` and `tool_output.products` is empty, and the query was for products):**
           *   **Output:** "I'm sorry, it looks like we're currently out of that. Let me check what other categories we have available that you might enjoy."
           *   **Action:** Immediately follow this by calling `execute_sql_query` again with the instruction to "list all available main categories".
       *   **If `execute_sql_query` returns a list of categories (i.e., `tool_output.status` is "success" and `tool_output.columns` contains "main_category"):**
           *   **Action:** Extract the `main_category` names from the rows.
           *   **Output:** "We might not have that specific item, but we have a great selection in categories like [Category1], [Category2], and [Category3]. Would you like to know more about any of these?" (List up to 3-5 categories, or say "many other categories" if too many).
       *   **If `execute_sql_query` returns an error (i.e., `tool_output.status` is "error"):**
           *   **Output:** "Apologies, my mind seems to be drawing a blank on that one at the moment. Could you ask in a different way?"

   6.  **How to Process and Respond to `recipe_agent_tool` Tool Output:**
   The result of recipe_agent_tool will be available in state with name recipies_data. Look for recipies_data in session state.
       *   **If `recipe_agent_tool` returns results:** Summarize the results conversationally.
       *   **If `recipe_agent_tool` returns no results:** "I couldn't find information on that. Could you try rephrasing?"
       *   **If the user's query was for a recipe and `recipe_agent_tool` returned results:** You MUST extract the relevant recipe text from the search results and format it into the specified JSON structure: `{{"status": "success", "data": {{"recepe": "<div>[extracted_recipe_text]</div>"}} }}`. You MUST output this JSON directly to the user as a `Content` event with `mime_type="application/json"`. No conversational text should precede this JSON output for recipe results.

   7.  **Dialogue Flow:** Keep your responses concise. After providing information, ask a question to keep the conversation going (e.g., "Does that sound good?", "What do you think?").
"""

root_agent = RootCoordinatorAgent(
    name="root_coordinator_agent",
    model="gemini-2.0-flash-live-001",
    description="Agent to answer questions using Google Search or query on database.",
    instruction=ROOT_AGENT_PROMPT.format(
        product_category=product_category,
        recommendation_preference=recommendation_preference,
        view_name=os.getenv("DB_VIEW_NAME", "recommendation_view")

    ),
    tools=[recipe_agent_tool, database_query_tool],
)
